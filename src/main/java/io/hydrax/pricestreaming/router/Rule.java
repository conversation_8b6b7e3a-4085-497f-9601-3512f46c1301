package io.hydrax.pricestreaming.router;

import io.hydrax.pricestreaming.common.OrderType;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.proto.metwo.match.PsOrder;
import io.hydrax.proto.metwo.match.PsOrderType;
import io.hydrax.proto.metwo.match.TimeInForce;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public interface Rule {
  String getCode();

  String getType();

  List<String> getTickers();

  Set<PsOrderType> getOrdTypes();

  Set<TimeInForce> getTimeInForces(String orderType);

  List<String> getVenueMarkets();

  default RouterHandler getRouterHandler() {
    return RouterHandlerFactory.getRouterHandler(this.getType());
  }

  default boolean match(Order order) {
    String orderType =
        switch (order.getPsOrder().getOrdType()) {
          case PS_ORDER_TYPE_MARKET -> OrderType.MARKET.getName();
          case PS_ORDER_TYPE_LIMIT -> OrderType.LIMIT.getName();
          case PS_ORDER_TYPE_STOP -> OrderType.STOP.getName();
          case UNRECOGNIZED -> null;
        };
    return this.getTimeInForces(orderType).contains(order.getPsOrder().getTimeInForce())
        && this.getOrdTypes().contains(order.getPsOrder().getOrdType())
        && this.getTickers().contains(order.getPsOrder().getSymbol());
  }

  default void handle(Order order, List<String> venueCodes) {
    this.getRouterHandler().handle(this.getCode(), order, venueCodes);
  }
}
