package io.hydrax.pricestreaming.cache;

import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.hydrax.pricestreaming.domain.OrsOrderType;
import io.hydrax.pricestreaming.domain.TradingVenueDTO;
import io.hydrax.pricestreaming.utils.BeanUtil;
import io.hydrax.proto.metwo.match.ConfigAction;
import io.hydrax.proto.metwo.match.TickerRoute;
import io.hydrax.proto.metwo.match.VenueMarketUpdateRequest;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

class TradingVenueDTOCacheTest {
  @InjectMocks TradingVenueCache tradingVenueCache;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void testSelectCodeByTimeInForceAndOrderType() {
    try (MockedStatic<BeanUtil> mocked = mockStatic(BeanUtil.class)) {
      mocked.when(() -> BeanUtil.getBean(ObjectMapper.class)).thenReturn(new ObjectMapper());
      tradingVenueCache.put(
          VenueMarketUpdateRequest.newBuilder()
              .setAction(ConfigAction.UPDATE)
              .setCode("test")
              .setDbId(1)
              .setName("test")
              .setMarketCode("SPOT")  // Add missing marketCode
              .setPriceStreamingOrderTypes(
                  """
{"market":{"enabled":true,"tifs":["gtc"]},"limit":{"enabled":true,"tifs":["fok","gtc","ioc"]},"stop":{"enabled":false,"tifs":[]}}
""")
              .addTickersRoute(
                  TickerRoute.newBuilder().setLpTickerName("11111").setTickerCode("ticker").build())
              .build());
      List<String> result =
          tradingVenueCache.selectCodeByTimeInForceAndOrderType("gtc", "limit", "ticker");
      Assertions.assertEquals(1, result.size());
    }
  }

  private TradingVenueDTO buildTradingVenue() {
    VenueMarketUpdateRequest venue =
        VenueMarketUpdateRequest.newBuilder()
            .setAction(ConfigAction.UPDATE)
            .setCode("test")
            .setDbId(1)
            .setName("test")
            .setMarketCode("SPOT")  // Add missing marketCode
            .setPriceStreamingOrderTypes(
                """
{"market":{"enabled":true,"tifs":["gtc"]},"limit":{"enabled":true,"tifs":["fok","gtc","ioc"]},"stop":{"enabled":false,"tifs":[]}}
""")
            .addTickersRoute(
                TickerRoute.newBuilder().setLpTickerName("11111").setTickerCode("ticker").build())
            .build();
    OrsOrderType orderType = OrsOrderType.parse(venue.getPriceStreamingOrderTypes());
    return TradingVenueDTO.builder()
        .id((int) venue.getDbId())
        .balanceCheckEnabled(venue.getBalanceCheck())
        .code(venue.getCode())
        .name(venue.getName())
        .status(venue.getStatus())
        .marketId((int) venue.getMarketId())
        .pricingStructure(venue.getPricingStructure())
        .priceStreamingOrderTypes(venue.getPriceStreamingOrderTypes())
        .timeInForces(orderType.getTimeInForces())
        .orderTypes(orderType.getOrderTypes())
        .tickersRoute(venue.getTickersRouteList())
        .build();
  }
}
